import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/dartMain/dartMain_screens/account_details_screen.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/SentApplications_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/readerMode/main_logic/account_details_logic.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';

class NavDrawerBasic extends ConsumerWidget {
  final FirebaseAuth auth = FirebaseAuth.instance;
  final String currentRoute;
  final FetchDatabase fetch = FetchDatabase();
  NavDrawerBasic({Key? key, required this.currentRoute}) : super(key: key);

  AuthenticationLogic authObject = AuthenticationLogic();
  AccountDetailsLogic accountDetailsLogic = AccountDetailsLogic();
  UniversalWidgets universals = UniversalWidgets();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return bookBranchNavDrawer(context, ref);
  }

  Widget bookBranchNavDrawer(BuildContext context, WidgetRef ref) {
    return Drawer(
      elevation: 2.0,
      child: Column(
        children: <Widget>[
          _buildUserInfoContainer(context, ref),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              children: <Widget>[
                const Padding(
                  padding: EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                  child: Text(
                    'NAVIGATION',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                _buildNavItem(
                  icon: Icons.home,
                  title: 'Home',
                  context: context,
                  route: MainScreen.routeName,
                  isSelected: currentRoute == MainScreen.routeName,
                ),
                _buildNavItem(
                  icon: Icons.subscriptions,
                  title: 'Subscriptions',
                  context: context,
                  route: SubscriptionsScreenReader.routeName,
                  isSelected:
                      currentRoute == SubscriptionsScreenReader.routeName,
                ),
                _buildNavItem(
                  icon: Icons.store,
                  title: 'Marketplace',
                  context: context,
                  route: MarketPlaceScreenReader.routeName,
                  isSelected: currentRoute == MarketPlaceScreenReader.routeName,
                ),
                _buildNavItem(
                  icon: Icons.pending,
                  title: 'Pending Applications',
                  context: context,
                  route: SentApplicationsScreenReader.routeName,
                  isSelected:
                      currentRoute == SentApplicationsScreenReader.routeName,
                ),
                const Divider(height: 32, thickness: 0.5),
                _buildMoreNavItem(context: context, isSelected: true),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _upgradeButton(
              ref,
              title: 'Get BookBranch+',
              icon: Icons.workspace_premium,
              route: MainScreen.routeName,
              context: context,
              isSelected: currentRoute == MainScreen.routeName,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String title,
    required BuildContext context,
    required String route,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: isSelected ? globals.bookBranchGreen.withAlpha(25) : null,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? globals.bookBranchGreen : Colors.grey[700],
          size: 22,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? globals.bookBranchGreen : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        onTap: () {
          Navigator.pushNamed(context, route);
        },
      ),
    );
  }

  Widget _buildMoreNavItem({
    required BuildContext context,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ExpansionTile(
        leading: Icon(
          Icons.more_horiz,
          color: Colors.grey[700],
          size: 22,
        ),
        title: Text(
          'More',
          style: TextStyle(
            color: Colors.grey[800],
            fontWeight: FontWeight.normal,
          ),
        ),
        iconColor: globals.bookBranchGreen,
        collapsedIconColor: Colors.grey[700],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        children: <Widget>[
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.help, color: Colors.grey[700], size: 22),
              title: Text('Help', style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                Navigator.pushNamed(context, '/helpScreen');
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.gavel, color: Colors.grey[700], size: 22),
              title: Text('Terms of Use',
                  style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                Navigator.pushNamed(context, '/legalScreen');
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.logout, color: Colors.grey[700], size: 22),
              title: Text('Logout', style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                authObject.logoutUser(ref);
                Navigator.pushNamed(context, '/Authentication');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _upgradeButton(WidgetRef ref,
      {required String title,
      required IconData icon,
      required String route,
      required bool isSelected,
      required BuildContext context}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: globals.bookBranchGreen,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(icon, color: Colors.white, size: 22),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        onTap: () {
          showPaywallPopup(ref);
        },
      ),
    );
  }

  Widget _buildUserInfoContainer(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              globals.bookBranchGreen.withAlpha(25),
              Colors.white,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return AccountDetailsScreen();
                  }));
                },
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 28,
                    backgroundColor: globals.bookBranchGreen,
                    child: CircleAvatar(
                      radius: 26,
                      backgroundColor: Colors.white,
                      child: Text(
                        ref.watch(currentUserFirstLetterProvider),
                        style: TextStyle(
                          color: globals.bookBranchGreen,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      ref.watch(displayNameProvider),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      FirebaseAuth.instance.currentUser?.email ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[600],
                ),
                onPressed: () {
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return AccountDetailsScreen();
                  }));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void showPaywallPopup(WidgetRef ref) async {
    try {
      // Show a loading indicator
      showDialog(
        context: navigatorKey.currentContext!,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Fetch offerings first to ensure they're loaded
      final offerings = await Purchases.getOfferings();

      // Close the loading indicator
      Navigator.of(navigatorKey.currentContext!).pop();

      if (offerings.current == null) {
        // Show error if no offerings are available
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          const SnackBar(
              content: Text(
                  'No subscription offerings available. Please try again later.')),
        );
        return;
      }

      // Present the paywall
      final paywallResult = await RevenueCatUI.presentPaywall();

      // Handle the result
      if (paywallResult == PaywallResult.purchased) {
        purchaseWasSuccessful(ref);
      } else if (paywallResult == PaywallResult.cancelled) {
        debugPrint('Purchase was cancelled by user');
      } else if (paywallResult == PaywallResult.error) {
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          const SnackBar(content: Text('Purchase failed. Please try again.')),
        );
      } else if (paywallResult == PaywallResult.restored) {
        purchaseWasSuccessful(ref);
      }
    } catch (e) {
      // Close the loading indicator if it's still showing
      if (Navigator.canPop(navigatorKey.currentContext!)) {
        Navigator.of(navigatorKey.currentContext!).pop();
      }

      // Show error message
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
      debugPrint('Error in showPaywallPopup: $e');
    }
  }

  void purchaseWasSuccessful(WidgetRef ref) async {
    CustomerInfo customerInfo = await Purchases.getCustomerInfo();

    if (customerInfo.entitlements.all["BookBranch+"]!.isActive) {
      accountDetailsLogic.upgradePlanType(ref);
    }
  }
}
