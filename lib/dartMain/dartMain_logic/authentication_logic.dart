import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/create_new_user.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/user_metadata_fetch.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_logic/on_login.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

//This class defines non-widget functions related to user authentication
class AuthenticationLogic {
  final FirebaseAuth auth = FirebaseAuth.instance;
  FirebaseFirestore db = FirebaseFirestore.instance;
  CreateNewUser write = CreateNewUser();
  UniversalWidgets universals = UniversalWidgets();
  OnLogin onLogin = OnLogin();

  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  Future<List<String>> getSignInMethodsForEmail(String email) async {
    try {
      return await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);
    } catch (e) {
      print("An error occurred while checking sign-in methods: $e");
      return [];
    }
  }

  Future<User?> handleSignInEmail(
    String email,
    String password,
    WidgetRef ref,
    BuildContext context,
  ) async {
    // Start showing the loader
    ref.read(isLoadingProvider.notifier).state = true;

    try {
      // Attempt sign-in
      UserCredential userCredential = await auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      globals.didSignIn = true;
      User? user = userCredential.user;

      if (user != null) {
        // Check if email is verified
        if (!user.emailVerified) {
          // Email not verified, return user but don't complete login
          return user;
        }

        // Update Firestore email verification status if needed
        await db
            .collection('users')
            .doc(user.uid)
            .update({'emailVerified': true});

        // Fetch user metadata
        String uid = user.uid;
        UserMetadataFetch? userMetadata =
            await UserMetadataFetch.fetchUserMetaData(uid);

        if (userMetadata != null) {
          String accountType = userMetadata.getPropertyValue('Account Type');
          // Update the Riverpod state
          ref.read(planTypeProvider.notifier).state = accountType;
        }

        // Cache username on login among other things
        onLogin.initializeProvidersOnLogin(ref);
      }

      return user;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'wrong-password') {
        List<String> signInMethods = await getSignInMethodsForEmail(email);
        if (signInMethods.contains('google.com')) {
          // Inform the user to use Google to sign in
          showSignInWithGooglePopup(context);
          print("Please use Google to sign in.");
        } else {
          // Handle other types of sign-in options or show a generic error
          print("Sign in failed with wrong password.");
        }
      }
      print(e);
      globals.didSignIn = false;
      return null;
    } finally {
      // Always stop showing the loader (success or error)
      ref.read(isLoadingProvider.notifier).state = false;
    }
  }

  Future<User?> signInWithGoogle(WidgetRef ref) async {
    ref.read(isLoadingProvider.notifier).state = true; // Start loading
    try {
      // 1) Prompt the user to pick a Google account
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        // User canceled the Google sign-in flow
        ref.read(isLoadingProvider.notifier).state = false;
        return null;
      }

      final String email = googleUser.email;

      // 2) Check if *any* Firebase Auth provider exists for this email
      final existingMethods =
          await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);

      if (existingMethods.isEmpty) {
        // Means NO account exists for this email in Firebase Auth
        // => Do NOT create a new user with Google
        await _googleSignIn.signOut();

        // Optionally notify user
        print("No existing account found for email $email. Sign-in blocked.");

        return null;
      }

      // 3) At least one sign-in method exists => proceed
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      User? user = userCredential.user;

      if (user != null) {
        globals.didSignIn = true;

        bool isNewUser = userCredential.additionalUserInfo?.isNewUser ?? false;
        if (isNewUser) {
          // This can happen if the user had an existing email/password account
          // and is now linking Google automatically. If you'd rather not
          // auto-link, handle or sign out here. Otherwise, it’s fine.
          await write.addUserData(user, ref);
        } else {
          // Existing user - retrieve plan, etc.
          String uid = user.uid;
          UserMetadataFetch? userMetadata =
              await UserMetadataFetch.fetchUserMetaData(uid);
          if (userMetadata != null) {
            String accountType = userMetadata.getPropertyValue('Account Type');
            ref.read(planTypeProvider.notifier).state = accountType;
          }
        }

        // Initialize providers after login
        onLogin.initializeProvidersOnLogin(ref);

        return user;
      }
    } on FirebaseAuthException catch (e) {
      print(e.message);
      globals.didSignIn = false;
    } catch (e) {
      print(e.toString());
    } finally {
      // Always end loading
      ref.read(isLoadingProvider.notifier).state = false;
    }
    return null;
  }

  Future<User?> signUpWithGoogle(WidgetRef ref) async {
    ref.read(isLoadingProvider.notifier).state = true; // Start loading
    try {
      // 1) Prompt the user to pick a Google account
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        // User canceled the Google sign-in flow
        ref.read(isLoadingProvider.notifier).state = false;
        return null;
      }

      final String email = googleUser.email;

      // 2) Check if account already exists for this email
      final existingMethods =
          await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);

      if (existingMethods.isNotEmpty) {
        // Account already exists for this email
        await _googleSignIn.signOut();
        print("Account already exists for email $email. Sign-up blocked.");
        return null;
      }

      // 3) No existing account => proceed with sign-up
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      User? user = userCredential.user;

      if (user != null) {
        globals.didSignIn = true;

        // Set account type to BookBranch
        ref.read(planTypeProvider.notifier).state = "BookBranch";

        // Add user data to Firestore (with emailVerified: false for verification flow)
        await write.addUserData(user, ref);

        // For Google users, we still want them to go through verification flow
        // even though Google may have already verified their email
        // Send email verification for consistency
        try {
          await user.sendEmailVerification();
        } catch (e) {
          // Google users might already be verified, but we still want the flow
          print('Email verification send failed (may already be verified): $e');
        }

        // Don't initialize providers yet - wait for verification flow completion

        print(
            'Google sign-up successful for BookBranch account: ${user.email}');
        return user;
      }
    } on FirebaseAuthException catch (e) {
      print(e.message);
      globals.didSignIn = false;
    } catch (e) {
      print(e.toString());
    } finally {
      // Always end loading
      ref.read(isLoadingProvider.notifier).state = false;
    }
    return null;
  }

  Future<User?> signUpWithGooglePlus(WidgetRef ref) async {
    // 1. Show the paywall first
    final paywallResult = await RevenueCatUI.presentPaywall();
    if (paywallResult != PaywallResult.purchased) {
      // If the user cancels or fails the purchase, stop here
      print('User did not complete purchase.');
      return null;
    }

    // 2. Confirm the user's entitlement is actually active
    final customerInfo = await Purchases.getCustomerInfo();
    final isBookBranchPlusActive =
        customerInfo.entitlements.all["BookBranch+"]?.isActive ?? false;

    if (!isBookBranchPlusActive) {
      print('Entitlement is not active, even though purchase was indicated.');
      return null;
    }

    ref.read(isLoadingProvider.notifier).state = true; // Start loading
    try {
      // 3) Prompt the user to pick a Google account
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        // User canceled the Google sign-in flow
        ref.read(isLoadingProvider.notifier).state = false;
        return null;
      }

      final String email = googleUser.email;

      // 4) Check if account already exists for this email
      final existingMethods =
          await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);

      if (existingMethods.isNotEmpty) {
        // Account already exists for this email
        await _googleSignIn.signOut();
        print("Account already exists for email $email. Sign-up blocked.");
        return null;
      }

      // 5) No existing account => proceed with sign-up
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      User? user = userCredential.user;

      if (user != null) {
        globals.didSignIn = true;

        // Set account type to BookBranch+
        ref.read(planTypeProvider.notifier).state = "BookBranch+";

        // Add user data to Firestore (with emailVerified: false for verification flow)
        await write.addUserDataPlus(user, ref);

        // For Google users, we still want them to go through verification flow
        // even though Google may have already verified their email
        // Send email verification for consistency
        try {
          await user.sendEmailVerification();
        } catch (e) {
          // Google users might already be verified, but we still want the flow
          print('Email verification send failed (may already be verified): $e');
        }

        // Don't initialize providers yet - wait for verification flow completion

        print(
            'Google sign-up successful for BookBranch+ account: ${user.email}');
        return user;
      }
    } on FirebaseAuthException catch (e) {
      print(e.message);
      globals.didSignIn = false;
    } catch (e) {
      print(e.toString());
    } finally {
      // Always end loading
      ref.read(isLoadingProvider.notifier).state = false;
    }
    return null;
  }

  Future<User?> handleSignUp(
      String email, String password, WidgetRef ref) async {
    User? user;
    ref.read(planTypeProvider.notifier).state = "BookBranch";

    try {
      UserCredential userCredential = await auth.createUserWithEmailAndPassword(
          email: email, password: password);

      user = userCredential.user;

      if (user != null) {
        // Send email verification
        await user.sendEmailVerification();

        // Add user data to Firestore (with emailVerified: false)
        await write.addUserData(user, ref);

        // Don't initialize providers on login yet - wait for email verification
        print('Signup successful. Verification email sent to ${user.email}');
        return user;
      }
    } on FirebaseAuthException catch (e) {
      if (e.message ==
          'An unknown error occurred: FirebaseError: Firebase: Password should be at least 6 characters (auth/weak-password).') {
        print('Password must be at least 6 characters long.');
      } else if (e.code == 'email-already-in-use') {
        print('The account already exists for that email.');
      } else if (e.code == 'unknown') {
        print(e.message);
      }
      return null;
    } catch (e) {
      print('Error during signup: $e');
      return null;
    }
    return null;
  }

  // Future<User> handleSignUpBookBranchPlus(
  //     String email, String password, WidgetRef ref) async {
  //   User? user;
  //   ref.read(planTypeProvider.notifier).state = "BookBranch+";

  //   try {
  //     UserCredential userCredential = await auth.createUserWithEmailAndPassword(
  //         email: email, password: password);

  //     user = userCredential.user;

  //     // cache username on login among other things
  //     onLogin.initializeProvidersOnLogin(ref);
  //   } on FirebaseAuthException catch (e) {
  //     if (e.message ==
  //         'An unknown error occurred: FirebaseError: Firebase: Password should be at least 6 characters (auth/weak-password).') {
  //       print('Password must be at least 6 characters long.');
  //     } else if (e.code == 'email-already-in-use') {
  //       print('The account already exists for that email.');
  //     } else if (e.code == 'unknown') {
  //       print(e.message);
  //     }
  //   }
  //   write.addUserDataPlus(user!, ref);
  //   return user;
  // }

  Future<User?> handleSignUpBookBranchPlus(
    String email,
    String password,
    String confirmPassword,
    WidgetRef ref,
  ) async {
    // 1. Basic field validation
    if (email.isEmpty || password.isEmpty || confirmPassword.isEmpty) {
      print('All fields are required.');
      return null; // or throw an exception, or return an error result
    }

    if (password != confirmPassword) {
      print('Passwords do not match.');
      return null;
    }

    if (password.length < 6) {
      print('Password must be at least 6 characters long.');
      return null;
    }

    // 2. Show the paywall
    final paywallResult = await RevenueCatUI.presentPaywall();
    if (paywallResult != PaywallResult.purchased) {
      // If the user cancels or fails the purchase, stop here
      print('User did not complete purchase.');
      return null;
    }

    // 3. Confirm the user’s entitlement is actually active
    final customerInfo = await Purchases.getCustomerInfo();
    final isBookBranchPlusActive =
        customerInfo.entitlements.all["BookBranch+"]?.isActive ?? false;

    if (!isBookBranchPlusActive) {
      print('Entitlement is not active, even though purchase was indicated.');
      return null;
    }

    // 4. If we reach here, purchase is done & entitlement active → proceed with sign-up
    User? user;
    try {
      UserCredential userCredential = await auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      user = userCredential.user;

      if (user != null) {
        // Send email verification
        await user.sendEmailVerification();

        // 5. Write user data (with emailVerified: false)
        // e.g., marking them as BookBranch+ in Firestore
        await write.addUserDataPlus(user, ref);

        // Update local state
        ref.read(planTypeProvider.notifier).state = "BookBranch+";

        print(
            'Sign-up successful. User has BookBranch+ entitlement. Verification email sent.');

        return user;
      }
    } on FirebaseAuthException catch (e) {
      // Handle Firebase sign-up errors
      if (e.code == 'email-already-in-use') {
        print('The account already exists for that email.');
      } else if (e.code == 'weak-password') {
        print('Password must be at least 6 characters long.');
      } else {
        print(e.message);
      }
      return null;
    } catch (e) {
      print('Error during BookBranch+ signup: $e');
      return null;
    }
    return null;
  }

  Future<void> logoutUser() async {
    final FirebaseAuth auth = FirebaseAuth.instance;
    final GoogleSignIn googleSignIn = GoogleSignIn();

    try {
      // Check if user is signed in with Google
      final GoogleSignInAccount? googleUser = await googleSignIn.signOut();

      // Sign out from Firebase
      await auth.signOut();
    } catch (error) {
      print("Logout Error: $error");
      throw Exception('Logout failed');
    }
  }

  void getAuthState() {
    auth.authStateChanges().listen((User? user) {
      if (user == null) {
        print('User is currently signed out!');
      } else {
        print('User is signed in!');
        print(user.uid);
      }
    });
  }

  // Method to update email verification status in Firestore
  Future<void> updateEmailVerificationStatus(
      String uid, bool isVerified) async {
    try {
      await db.collection('users').doc(uid).update({
        'emailVerified': isVerified,
      });
      print('Email verification status updated in Firestore');
    } catch (e) {
      print('Error updating email verification status: $e');
    }
  }

  // Method to check if user's email is verified and update Firestore accordingly
  Future<bool> checkAndUpdateEmailVerification(User user) async {
    try {
      await user.reload();
      final currentUser = FirebaseAuth.instance.currentUser;

      if (currentUser != null && currentUser.emailVerified) {
        await updateEmailVerificationStatus(currentUser.uid, true);
        return true;
      }
      return false;
    } catch (e) {
      print('Error checking email verification: $e');
      return false;
    }
  }

  // would like to move this elsewhere
  void showSignInWithGooglePopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
          child: Container(
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Text(
                  'The sign-in method chosen does not match our records. Please try another sign-in method or reset your password if you have forgotten it.',
                  style: TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                universals.buildButtonFlatWidth(
                  "OK",
                  true,
                  () {
                    Navigator.of(dialogContext)
                        .pop(); // Dismiss the dialog on press
                  },
                  globals.bookBranchGreen,
                  Colors.white,
                  dialogContext,
                  0.75, // widthFactor as 75% of screen width
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
